namespace Ngp.Communication.ModbusTcpMaster.Events;

using Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Base class for all Modbus events
/// </summary>
public abstract class ModbusEventArgs : EventArgs
{
    /// <summary>
    /// IP address of the device
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// Port number
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }
    
    /// <summary>
    /// Timestamp when the event occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Event arguments for data value updates
/// </summary>
public class DataValueUpdatedEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Type of register that was updated
    /// </summary>
    public ModbusRegisterType RegisterType { get; set; }
    
    /// <summary>
    /// Starting address of the updated data
    /// </summary>
    public ushort StartAddress { get; set; }
    
    /// <summary>
    /// Updated values (for coils/discrete inputs)
    /// </summary>
    public bool[]? CoilValues { get; set; }
    
    /// <summary>
    /// Updated values (for registers)
    /// </summary>
    public ushort[]? RegisterValues { get; set; }
    
    /// <summary>
    /// Raw data bytes
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();
}

/// <summary>
/// Event arguments for connection status changes
/// </summary>
public class ConnectionStatusChangedEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Previous connection status
    /// </summary>
    public ConnectionStatus PreviousStatus { get; set; }
    
    /// <summary>
    /// Current connection status
    /// </summary>
    public ConnectionStatus CurrentStatus { get; set; }
    
    /// <summary>
    /// Error message if connection failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Exception that caused the status change (if any)
    /// </summary>
    public Exception? Exception { get; set; }
}

/// <summary>
/// Event arguments for Modbus exceptions
/// </summary>
public class ModbusExceptionEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Function code that caused the exception
    /// </summary>
    public ModbusFunctionCode FunctionCode { get; set; }
    
    /// <summary>
    /// Modbus exception code
    /// </summary>
    public ModbusExceptionCode ExceptionCode { get; set; }
    
    /// <summary>
    /// Starting address of the request that caused the exception
    /// </summary>
    public ushort StartAddress { get; set; }
    
    /// <summary>
    /// Quantity of registers/coils in the request
    /// </summary>
    public ushort Quantity { get; set; }
    
    /// <summary>
    /// Transaction ID of the request
    /// </summary>
    public ushort TransactionId { get; set; }
}

/// <summary>
/// Event arguments for communication errors
/// </summary>
public class CommunicationErrorEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Type of error
    /// </summary>
    public CommunicationErrorType ErrorType { get; set; }
    
    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// Exception that caused the error
    /// </summary>
    public Exception? Exception { get; set; }
    
    /// <summary>
    /// Request that caused the error (if applicable)
    /// </summary>
    public ModbusRequest? Request { get; set; }
    
    /// <summary>
    /// Response that contained the error (if applicable)
    /// </summary>
    public ModbusResponse? Response { get; set; }
}

/// <summary>
/// Event arguments for CRC errors
/// </summary>
public class CrcErrorEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Expected CRC value
    /// </summary>
    public ushort ExpectedCrc { get; set; }
    
    /// <summary>
    /// Actual CRC value
    /// </summary>
    public ushort ActualCrc { get; set; }
    
    /// <summary>
    /// Raw data that failed CRC check
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();
    
    /// <summary>
    /// Transaction ID of the request
    /// </summary>
    public ushort TransactionId { get; set; }
}

/// <summary>
/// Event arguments for timeout events
/// </summary>
public class TimeoutEventArgs : ModbusEventArgs
{
    /// <summary>
    /// Request that timed out
    /// </summary>
    public ModbusRequest Request { get; set; } = new();
    
    /// <summary>
    /// Timeout duration
    /// </summary>
    public TimeSpan Timeout { get; set; }
    
    /// <summary>
    /// Time elapsed since request was sent
    /// </summary>
    public TimeSpan ElapsedTime { get; set; }
}

/// <summary>
/// Types of communication errors
/// </summary>
public enum CommunicationErrorType
{
    ConnectionLost,
    SendFailed,
    ReceiveFailed,
    InvalidResponse,
    Timeout,
    CrcError,
    ProtocolError,
    UnexpectedResponse
}
