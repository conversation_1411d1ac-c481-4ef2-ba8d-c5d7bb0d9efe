namespace Ngp.Communication.ModbusTcpMaster.Engine;

using System.Collections.Concurrent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Engine for polling Modbus registers automatically
/// </summary>
public class RegisterPollingEngine : IDisposable
{
    private readonly ModbusMaster _modbusMaster;
    private readonly ConcurrentDictionary<string, List<RegisterRange>> _pollingRanges = new();
    private readonly ConcurrentDictionary<string, Dictionary<string, object>> _lastValues = new();
    private readonly object _lockObject = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly SemaphoreSlim _pollingSemaphore;
    
    private Task? _pollingTask;
    private bool _isRunning = false;
    private bool _disposed = false;
    
    /// <summary>
    /// Polling interval for checking due ranges
    /// </summary>
    public TimeSpan PollingCheckInterval { get; set; } = TimeSpan.FromMilliseconds(100);
    
    /// <summary>
    /// Maximum number of concurrent polling operations
    /// </summary>
    public int MaxConcurrentPolling { get; set; } = 50;
    
    /// <summary>
    /// Enable change detection (only raise events when values change)
    /// </summary>
    public bool EnableChangeDetection { get; set; } = true;
    
    /// <summary>
    /// Maximum register range size for optimization
    /// </summary>
    public ushort MaxRegisterRangeSize { get; set; } = 100;
    
    /// <summary>
    /// Maximum coil range size for optimization
    /// </summary>
    public ushort MaxCoilRangeSize { get; set; } = 1000;
    
    /// <summary>
    /// Event raised when data values are updated
    /// </summary>
    public event EventHandler<DataValueUpdatedEventArgs>? DataValueUpdated;
    
    public RegisterPollingEngine(ModbusMaster modbusMaster)
    {
        _modbusMaster = modbusMaster ?? throw new ArgumentNullException(nameof(modbusMaster));
        _pollingSemaphore = new SemaphoreSlim(MaxConcurrentPolling, MaxConcurrentPolling);
    }
    
    /// <summary>
    /// Start the polling engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the start operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RegisterPollingEngine));
        
        if (_isRunning)
            return;
        
        _isRunning = true;
        _pollingTask = Task.Run(() => PollingLoop(_cancellationTokenSource.Token), cancellationToken);
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Stop the polling engine
    /// </summary>
    /// <returns>Task representing the stop operation</returns>
    public async Task StopAsync()
    {
        if (!_isRunning)
            return;
        
        _isRunning = false;
        _cancellationTokenSource.Cancel();
        
        if (_pollingTask != null)
        {
            try
            {
                await _pollingTask;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }
    }
    
    /// <summary>
    /// Add a register range for polling
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="registerRange">Register range to poll</param>
    public void AddPollingRange(string ipAddress, int port, RegisterRange registerRange)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RegisterPollingEngine));
        
        var key = $"{ipAddress}:{port}";
        
        lock (_lockObject)
        {
            if (!_pollingRanges.TryGetValue(key, out var ranges))
            {
                ranges = new List<RegisterRange>();
                _pollingRanges[key] = ranges;
            }
            
            // Check if range already exists
            if (!ranges.Any(r => r.Equals(registerRange)))
            {
                // Split range if it's too large
                var optimizedRanges = OptimizeRegisterRange(registerRange);
                ranges.AddRange(optimizedRanges);
            }
        }
    }
    
    /// <summary>
    /// Remove a register range from polling
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="registerRange">Register range to remove</param>
    public void RemovePollingRange(string ipAddress, int port, RegisterRange registerRange)
    {
        if (_disposed)
            return;
        
        var key = $"{ipAddress}:{port}";
        
        lock (_lockObject)
        {
            if (_pollingRanges.TryGetValue(key, out var ranges))
            {
                ranges.RemoveAll(r => r.Equals(registerRange));
                
                if (ranges.Count == 0)
                {
                    _pollingRanges.TryRemove(key, out _);
                }
            }
        }
    }
    
    /// <summary>
    /// Get all polling ranges for a device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <returns>Collection of register ranges</returns>
    public IEnumerable<RegisterRange> GetPollingRanges(string ipAddress, int port)
    {
        var key = $"{ipAddress}:{port}";
        
        if (_pollingRanges.TryGetValue(key, out var ranges))
        {
            lock (_lockObject)
            {
                return ranges.ToArray();
            }
        }
        
        return Enumerable.Empty<RegisterRange>();
    }
    
    /// <summary>
    /// Clear all polling ranges
    /// </summary>
    public void ClearAllRanges()
    {
        lock (_lockObject)
        {
            _pollingRanges.Clear();
            _lastValues.Clear();
        }
    }
    
    /// <summary>
    /// Main polling loop
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the polling loop</returns>
    private async Task PollingLoop(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested && _isRunning)
        {
            try
            {
                var pollingTasks = new List<Task>();
                
                // Get all ranges that are due for polling
                foreach (var deviceRanges in _pollingRanges.ToArray())
                {
                    var deviceKey = deviceRanges.Key;
                    var ranges = deviceRanges.Value.ToArray();
                    
                    foreach (var range in ranges)
                    {
                        if (range.IsPollingDue() && !range.IsPolling)
                        {
                            var pollingTask = PollRegisterRange(deviceKey, range, cancellationToken);
                            pollingTasks.Add(pollingTask);
                        }
                    }
                }
                
                // Wait for all polling tasks to complete or timeout
                if (pollingTasks.Count > 0)
                {
                    await Task.WhenAll(pollingTasks);
                }
                
                // Wait before next polling check
                await Task.Delay(PollingCheckInterval, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                // Log error and continue polling
                // In a real implementation, you might want to use a proper logging framework
                Console.WriteLine($"Error in polling loop: {ex.Message}");
                await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);
            }
        }
    }
    
    /// <summary>
    /// Poll a specific register range
    /// </summary>
    /// <param name="deviceKey">Device key (IP:Port)</param>
    /// <param name="range">Register range to poll</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the polling operation</returns>
    private async Task PollRegisterRange(string deviceKey, RegisterRange range, CancellationToken cancellationToken)
    {
        await _pollingSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            range.IsPolling = true;
            
            var parts = deviceKey.Split(':');
            var ipAddress = parts[0];
            var port = int.Parse(parts[1]);
            
            object? newValues = null;
            
            // Read the register range based on type
            switch (range.RegisterType)
            {
                case ModbusRegisterType.Coil:
                    newValues = await _modbusMaster.ReadCoilsAsync(ipAddress, port, range.UnitId, range.StartAddress, range.Count, cancellationToken: cancellationToken);
                    break;
                    
                case ModbusRegisterType.DiscreteInput:
                    newValues = await _modbusMaster.ReadDiscreteInputsAsync(ipAddress, port, range.UnitId, range.StartAddress, range.Count, cancellationToken: cancellationToken);
                    break;
                    
                case ModbusRegisterType.HoldingRegister:
                    newValues = await _modbusMaster.ReadHoldingRegistersAsync(ipAddress, port, range.UnitId, range.StartAddress, range.Count, cancellationToken: cancellationToken);
                    break;
                    
                case ModbusRegisterType.InputRegister:
                    newValues = await _modbusMaster.ReadInputRegistersAsync(ipAddress, port, range.UnitId, range.StartAddress, range.Count, cancellationToken: cancellationToken);
                    break;
            }
            
            range.LastPolled = DateTime.UtcNow;
            
            // Check for value changes and raise events
            if (newValues != null)
            {
                await HandleValueUpdate(deviceKey, range, newValues, ipAddress, port);
            }
        }
        catch (Exception ex)
        {
            // Handle polling errors
            // In a real implementation, you might want to use a proper logging framework
            Console.WriteLine($"Error polling range {range}: {ex.Message}");
        }
        finally
        {
            range.IsPolling = false;
            _pollingSemaphore.Release();
        }
    }

    /// <summary>
    /// Handle value updates and change detection
    /// </summary>
    /// <param name="deviceKey">Device key</param>
    /// <param name="range">Register range</param>
    /// <param name="newValues">New values</param>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Task representing the value update handling</returns>
    private Task HandleValueUpdate(string deviceKey, RegisterRange range, object newValues, string ipAddress, int port)
    {
        var rangeKey = $"{deviceKey}:{range.GetIdentifier()}";

        bool hasChanged = true;

        if (EnableChangeDetection)
        {
            if (_lastValues.TryGetValue(rangeKey, out var lastValuesDict))
            {
                if (lastValuesDict.TryGetValue("values", out var lastValues))
                {
                    hasChanged = !ValuesEqual(lastValues, newValues);
                }
            }
            else
            {
                _lastValues[rangeKey] = new Dictionary<string, object>();
            }

            _lastValues[rangeKey]["values"] = newValues;
        }

        if (hasChanged)
        {
            var eventArgs = new DataValueUpdatedEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = range.UnitId,
                RegisterType = range.RegisterType,
                StartAddress = range.StartAddress
            };

            // Set appropriate values based on register type
            if (range.RegisterType == ModbusRegisterType.Coil || range.RegisterType == ModbusRegisterType.DiscreteInput)
            {
                eventArgs.CoilValues = (bool[])newValues;
                eventArgs.RawData = ConvertCoilsToBytes(eventArgs.CoilValues);
            }
            else
            {
                eventArgs.RegisterValues = (ushort[])newValues;
                eventArgs.RawData = ConvertRegistersToBytes(eventArgs.RegisterValues);
            }

            // Raise event asynchronously to avoid blocking polling
            _ = Task.Run(() => DataValueUpdated?.Invoke(this, eventArgs));
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Compare two value arrays for equality
    /// </summary>
    /// <param name="values1">First value array</param>
    /// <param name="values2">Second value array</param>
    /// <returns>True if values are equal</returns>
    private static bool ValuesEqual(object values1, object values2)
    {
        if (values1 is bool[] bools1 && values2 is bool[] bools2)
        {
            return bools1.SequenceEqual(bools2);
        }

        if (values1 is ushort[] ushorts1 && values2 is ushort[] ushorts2)
        {
            return ushorts1.SequenceEqual(ushorts2);
        }

        return false;
    }

    /// <summary>
    /// Optimize register range by splitting if necessary
    /// </summary>
    /// <param name="range">Original register range</param>
    /// <returns>Collection of optimized ranges</returns>
    private IEnumerable<RegisterRange> OptimizeRegisterRange(RegisterRange range)
    {
        var maxSize = range.RegisterType == ModbusRegisterType.Coil || range.RegisterType == ModbusRegisterType.DiscreteInput
            ? MaxCoilRangeSize
            : MaxRegisterRangeSize;

        if (range.Count <= maxSize)
        {
            yield return range;
            yield break;
        }

        // Split into smaller ranges
        var currentAddress = range.StartAddress;
        var remainingCount = range.Count;

        while (remainingCount > 0)
        {
            var currentCount = Math.Min(remainingCount, maxSize);

            yield return new RegisterRange
            {
                RegisterType = range.RegisterType,
                StartAddress = currentAddress,
                Count = (ushort)currentCount,
                UnitId = range.UnitId,
                PollingInterval = range.PollingInterval
            };

            currentAddress += (ushort)currentCount;
            remainingCount -= (ushort)currentCount;
        }
    }

    /// <summary>
    /// Convert coil values to byte array
    /// </summary>
    /// <param name="values">Coil values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertCoilsToBytes(bool[] values)
    {
        var byteCount = (values.Length + 7) / 8;
        var bytes = new byte[byteCount];

        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                bytes[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        return bytes;
    }

    /// <summary>
    /// Convert register values to byte array
    /// </summary>
    /// <param name="values">Register values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertRegistersToBytes(ushort[] values)
    {
        var bytes = new byte[values.Length * 2];

        for (int i = 0; i < values.Length; i++)
        {
            var registerBytes = BitConverter.GetBytes(values[i]);
            if (BitConverter.IsLittleEndian)
            {
                bytes[i * 2] = registerBytes[1];
                bytes[i * 2 + 1] = registerBytes[0];
            }
            else
            {
                bytes[i * 2] = registerBytes[0];
                bytes[i * 2 + 1] = registerBytes[1];
            }
        }

        return bytes;
    }

    /// <summary>
    /// Dispose of the polling engine
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            StopAsync().Wait(TimeSpan.FromSeconds(5));
        }
        catch
        {
            // Ignore disposal errors
        }

        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
        _pollingSemaphore.Dispose();

        GC.SuppressFinalize(this);
    }
}
