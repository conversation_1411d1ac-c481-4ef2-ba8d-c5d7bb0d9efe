namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a Modbus response
/// </summary>
public class ModbusResponse
{
    /// <summary>
    /// Transaction identifier for Modbus TCP
    /// </summary>
    public ushort TransactionId { get; set; }
    
    /// <summary>
    /// Protocol identifier (0 for Modbus)
    /// </summary>
    public ushort ProtocolId { get; set; }
    
    /// <summary>
    /// Length of the following bytes
    /// </summary>
    public ushort Length { get; set; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }
    
    /// <summary>
    /// Function code
    /// </summary>
    public ModbusFunctionCode FunctionCode { get; set; }
    
    /// <summary>
    /// Indicates if this is an exception response
    /// </summary>
    public bool IsException { get; set; }
    
    /// <summary>
    /// Exception code if this is an exception response
    /// </summary>
    public ModbusExceptionCode? ExceptionCode { get; set; }
    
    /// <summary>
    /// Response data
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();
    
    /// <summary>
    /// Timestamp when the response was received
    /// </summary>
    public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Parse a byte array into a ModbusResponse
    /// </summary>
    /// <param name="buffer">The byte array to parse</param>
    /// <returns>Parsed ModbusResponse</returns>
    public static ModbusResponse Parse(byte[] buffer)
    {
        if (buffer.Length < 8)
            throw new ArgumentException("Invalid Modbus response: too short");
        
        var response = new ModbusResponse();
        
        // Parse MBAP Header
        response.TransactionId = (ushort)((buffer[0] << 8) | buffer[1]);
        response.ProtocolId = (ushort)((buffer[2] << 8) | buffer[3]);
        response.Length = (ushort)((buffer[4] << 8) | buffer[5]);
        response.UnitId = buffer[6];
        
        // Parse PDU
        var functionCode = buffer[7];
        response.IsException = (functionCode & 0x80) != 0;
        
        if (response.IsException)
        {
            response.FunctionCode = (ModbusFunctionCode)(functionCode & 0x7F);
            if (buffer.Length >= 9)
            {
                response.ExceptionCode = (ModbusExceptionCode)buffer[8];
            }
        }
        else
        {
            response.FunctionCode = (ModbusFunctionCode)functionCode;
            
            // Extract data based on function code
            if (buffer.Length > 8)
            {
                response.Data = new byte[buffer.Length - 8];
                Array.Copy(buffer, 8, response.Data, 0, response.Data.Length);
            }
        }
        
        return response;
    }
    
    /// <summary>
    /// Get coil values from response data
    /// </summary>
    /// <returns>Array of boolean values representing coil states</returns>
    public bool[] GetCoilValues()
    {
        if (FunctionCode != ModbusFunctionCode.ReadCoils && 
            FunctionCode != ModbusFunctionCode.ReadDiscreteInputs)
            throw new InvalidOperationException("This method is only valid for coil/discrete input responses");
        
        if (Data.Length < 1)
            return Array.Empty<bool>();
        
        var byteCount = Data[0];
        var values = new List<bool>();
        
        for (int i = 1; i <= byteCount; i++)
        {
            if (i < Data.Length)
            {
                var dataByte = Data[i];
                for (int bit = 0; bit < 8; bit++)
                {
                    values.Add((dataByte & (1 << bit)) != 0);
                }
            }
        }
        
        return values.ToArray();
    }
    
    /// <summary>
    /// Get register values from response data
    /// </summary>
    /// <returns>Array of ushort values representing register values</returns>
    public ushort[] GetRegisterValues()
    {
        if (FunctionCode != ModbusFunctionCode.ReadHoldingRegisters && 
            FunctionCode != ModbusFunctionCode.ReadInputRegisters)
            throw new InvalidOperationException("This method is only valid for register responses");
        
        if (Data.Length < 1)
            return Array.Empty<ushort>();
        
        var byteCount = Data[0];
        var values = new List<ushort>();
        
        for (int i = 1; i < Data.Length; i += 2)
        {
            if (i + 1 < Data.Length)
            {
                var value = (ushort)((Data[i] << 8) | Data[i + 1]);
                values.Add(value);
            }
        }
        
        return values.ToArray();
    }
}
