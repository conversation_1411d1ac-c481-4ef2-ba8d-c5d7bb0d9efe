namespace Ngp.Communication.ModbusTcpMaster.Engine;

using System.Collections.Concurrent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Exceptions;

/// <summary>
/// Main Modbus TCP Master engine
/// </summary>
public class ModbusMaster : IDisposable
{
    private readonly TcpConnectionManager _connectionManager;
    private readonly RegisterPollingEngine _pollingEngine;
    private readonly object _lockObject = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private ushort _nextTransactionId = 1;
    private bool _disposed = false;
    
    /// <summary>
    /// Protocol mode (TCP or RTU over TCP)
    /// </summary>
    public ModbusProtocolMode ProtocolMode { get; set; } = ModbusProtocolMode.ModbusTcp;
    
    /// <summary>
    /// Default timeout for requests
    /// </summary>
    public TimeSpan DefaultTimeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Gap time between requests
    /// </summary>
    public TimeSpan GapTime { get; set; } = TimeSpan.FromMilliseconds(10);
    
    /// <summary>
    /// Enable parallel processing
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;
    
    /// <summary>
    /// Maximum number of parallel requests
    /// </summary>
    public int MaxParallelRequests { get; set; } = 100;
    
    /// <summary>
    /// Connection manager
    /// </summary>
    public TcpConnectionManager ConnectionManager => _connectionManager;
    
    /// <summary>
    /// Polling engine
    /// </summary>
    public RegisterPollingEngine PollingEngine => _pollingEngine;
    
    /// <summary>
    /// Event raised when data values are updated
    /// </summary>
    public event EventHandler<DataValueUpdatedEventArgs>? DataValueUpdated;
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Event raised when a Modbus exception occurs
    /// </summary>
    public event EventHandler<ModbusExceptionEventArgs>? ModbusExceptionOccurred;
    
    /// <summary>
    /// Event raised when a communication error occurs
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
    
    /// <summary>
    /// Event raised when a timeout occurs
    /// </summary>
    public event EventHandler<TimeoutEventArgs>? TimeoutOccurred;
    
    public ModbusMaster()
    {
        _connectionManager = new TcpConnectionManager();
        _pollingEngine = new RegisterPollingEngine(this);
        
        // Subscribe to events
        _connectionManager.ConnectionStatusChanged += OnConnectionStatusChanged;
        _connectionManager.CommunicationError += OnCommunicationError;
        _pollingEngine.DataValueUpdated += OnDataValueUpdated;
    }
    
    /// <summary>
    /// Read coils from a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of coils to read</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of coil values</returns>
    public async Task<bool[]> ReadCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ValidateReadParameters(startAddress, quantity, ModbusRegisterType.Coil);
        
        var request = CreateRequest(ModbusFunctionCode.ReadCoils, unitId, startAddress, quantity, timeout);
        var response = await SendRequestAsync(ipAddress, port, request, cancellationToken);
        
        return response.GetCoilValues().Take(quantity).ToArray();
    }
    
    /// <summary>
    /// Read discrete inputs from a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of discrete inputs to read</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of discrete input values</returns>
    public async Task<bool[]> ReadDiscreteInputsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ValidateReadParameters(startAddress, quantity, ModbusRegisterType.DiscreteInput);
        
        var request = CreateRequest(ModbusFunctionCode.ReadDiscreteInputs, unitId, startAddress, quantity, timeout);
        var response = await SendRequestAsync(ipAddress, port, request, cancellationToken);
        
        return response.GetCoilValues().Take(quantity).ToArray();
    }
    
    /// <summary>
    /// Read holding registers from a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of register values</returns>
    public async Task<ushort[]> ReadHoldingRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ValidateReadParameters(startAddress, quantity, ModbusRegisterType.HoldingRegister);
        
        var request = CreateRequest(ModbusFunctionCode.ReadHoldingRegisters, unitId, startAddress, quantity, timeout);
        var response = await SendRequestAsync(ipAddress, port, request, cancellationToken);
        
        return response.GetRegisterValues().Take(quantity).ToArray();
    }
    
    /// <summary>
    /// Read input registers from a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of registers to read</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Array of register values</returns>
    public async Task<ushort[]> ReadInputRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ValidateReadParameters(startAddress, quantity, ModbusRegisterType.InputRegister);
        
        var request = CreateRequest(ModbusFunctionCode.ReadInputRegisters, unitId, startAddress, quantity, timeout);
        var response = await SendRequestAsync(ipAddress, port, request, cancellationToken);
        
        return response.GetRegisterValues().Take(quantity).ToArray();
    }
    
    /// <summary>
    /// Write a single coil to a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the write operation</returns>
    public async Task WriteSingleCoilAsync(string ipAddress, int port, byte unitId, ushort address, bool value, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var data = new byte[] { (byte)(value ? 1 : 0) };
        var request = CreateRequest(ModbusFunctionCode.WriteSingleCoil, unitId, address, 1, timeout, data);
        await SendRequestAsync(ipAddress, port, request, cancellationToken);
    }
    
    /// <summary>
    /// Write a single register to a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the write operation</returns>
    public async Task WriteSingleRegisterAsync(string ipAddress, int port, byte unitId, ushort address, ushort value, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var data = BitConverter.GetBytes(value).Reverse().ToArray();
        var request = CreateRequest(ModbusFunctionCode.WriteSingleRegister, unitId, address, 1, timeout, data);
        await SendRequestAsync(ipAddress, port, request, cancellationToken);
    }
    
    /// <summary>
    /// Write multiple coils to a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the write operation</returns>
    public async Task WriteMultipleCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, bool[] values, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (values == null || values.Length == 0)
            throw new ArgumentException("Values cannot be null or empty", nameof(values));
        
        var data = ConvertCoilsToBytes(values);
        var request = CreateRequest(ModbusFunctionCode.WriteMultipleCoils, unitId, startAddress, (ushort)values.Length, timeout, data);
        await SendRequestAsync(ipAddress, port, request, cancellationToken);
    }
    
    /// <summary>
    /// Write multiple registers to a Modbus device
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <param name="timeout">Request timeout (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the write operation</returns>
    public async Task WriteMultipleRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort[] values, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (values == null || values.Length == 0)
            throw new ArgumentException("Values cannot be null or empty", nameof(values));

        var data = ConvertRegistersToBytes(values);
        var request = CreateRequest(ModbusFunctionCode.WriteMultipleRegisters, unitId, startAddress, (ushort)values.Length, timeout, data);
        await SendRequestAsync(ipAddress, port, request, cancellationToken);
    }

    /// <summary>
    /// Start the Modbus master engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the start operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusMaster));

        await _pollingEngine.StartAsync(cancellationToken);
    }

    /// <summary>
    /// Stop the Modbus master engine
    /// </summary>
    /// <returns>Task representing the stop operation</returns>
    public async Task StopAsync()
    {
        if (_disposed)
            return;

        await _pollingEngine.StopAsync();
        await _connectionManager.DisconnectAllAsync();
    }

    /// <summary>
    /// Add a register range for polling
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="registerRange">Register range to poll</param>
    public void AddPollingRange(string ipAddress, int port, RegisterRange registerRange)
    {
        _pollingEngine.AddPollingRange(ipAddress, port, registerRange);
    }

    /// <summary>
    /// Remove a register range from polling
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="registerRange">Register range to remove</param>
    public void RemovePollingRange(string ipAddress, int port, RegisterRange registerRange)
    {
        _pollingEngine.RemovePollingRange(ipAddress, port, registerRange);
    }

    /// <summary>
    /// Create a Modbus request
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Quantity of registers/coils</param>
    /// <param name="timeout">Request timeout</param>
    /// <param name="data">Data for write operations</param>
    /// <returns>Modbus request</returns>
    private ModbusRequest CreateRequest(ModbusFunctionCode functionCode, byte unitId, ushort startAddress, ushort quantity, TimeSpan? timeout = null, byte[]? data = null)
    {
        return new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = functionCode,
            StartingAddress = startAddress,
            Quantity = quantity,
            Data = data,
            Timeout = timeout ?? DefaultTimeout
        };
    }

    /// <summary>
    /// Send a request and handle the response
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="request">Modbus request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Modbus response</returns>
    private async Task<ModbusResponse> SendRequestAsync(string ipAddress, int port, ModbusRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Apply gap time if not parallel processing
            if (!EnableParallelProcessing && GapTime > TimeSpan.Zero)
            {
                await Task.Delay(GapTime, cancellationToken);
            }

            var response = await _connectionManager.SendRequestAsync(ipAddress, port, request, cancellationToken);

            // Handle exception responses
            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = request.UnitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = request.StartingAddress,
                    Quantity = request.Quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);

                throw new ModbusSlaveException(
                    request.FunctionCode,
                    response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    request.UnitId);
            }

            return response;
        }
        catch (ModbusTimeoutException)
        {
            var timeoutArgs = new TimeoutEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = request.UnitId,
                Request = request,
                Timeout = request.Timeout,
                ElapsedTime = DateTime.UtcNow - request.CreatedAt
            };

            TimeoutOccurred?.Invoke(this, timeoutArgs);
            throw;
        }
    }

    /// <summary>
    /// Get the next transaction ID
    /// </summary>
    /// <returns>Next transaction ID</returns>
    private ushort GetNextTransactionId()
    {
        lock (_lockObject)
        {
            return _nextTransactionId++;
        }
    }

    /// <summary>
    /// Validate read parameters
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Quantity</param>
    /// <param name="registerType">Register type</param>
    private static void ValidateReadParameters(ushort startAddress, ushort quantity, ModbusRegisterType registerType)
    {
        if (quantity == 0)
            throw new ArgumentException("Quantity must be greater than 0", nameof(quantity));

        var maxQuantity = registerType == ModbusRegisterType.Coil || registerType == ModbusRegisterType.DiscreteInput
            ? RegisterRange.MaxCoilCount
            : RegisterRange.MaxRegisterCount;

        if (quantity > maxQuantity)
            throw new ModbusInvalidRangeException(startAddress, quantity, registerType,
                $"Quantity {quantity} exceeds maximum allowed {maxQuantity} for {registerType}");

        if ((long)startAddress + quantity > 65536)
            throw new ModbusInvalidRangeException(startAddress, quantity, registerType,
                "Address range exceeds maximum Modbus address space");
    }

    /// <summary>
    /// Convert coil values to byte array
    /// </summary>
    /// <param name="values">Coil values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertCoilsToBytes(bool[] values)
    {
        var byteCount = (values.Length + 7) / 8;
        var bytes = new byte[byteCount];

        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                bytes[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        return bytes;
    }

    /// <summary>
    /// Convert register values to byte array
    /// </summary>
    /// <param name="values">Register values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertRegistersToBytes(ushort[] values)
    {
        var bytes = new byte[values.Length * 2];

        for (int i = 0; i < values.Length; i++)
        {
            var registerBytes = BitConverter.GetBytes(values[i]);
            if (BitConverter.IsLittleEndian)
            {
                bytes[i * 2] = registerBytes[1];
                bytes[i * 2 + 1] = registerBytes[0];
            }
            else
            {
                bytes[i * 2] = registerBytes[0];
                bytes[i * 2 + 1] = registerBytes[1];
            }
        }

        return bytes;
    }

    /// <summary>
    /// Handle connection status change events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        ConnectionStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Handle communication error events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnCommunicationError(object? sender, CommunicationErrorEventArgs e)
    {
        CommunicationError?.Invoke(this, e);
    }

    /// <summary>
    /// Handle data value updated events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnDataValueUpdated(object? sender, DataValueUpdatedEventArgs e)
    {
        DataValueUpdated?.Invoke(this, e);
    }

    /// <summary>
    /// Dispose of the Modbus master
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _cancellationTokenSource.Cancel();

        try
        {
            StopAsync().Wait(TimeSpan.FromSeconds(5));
        }
        catch
        {
            // Ignore disposal errors
        }

        _pollingEngine.Dispose();
        _connectionManager.Dispose();
        _cancellationTokenSource.Dispose();

        GC.SuppressFinalize(this);
    }
}
