1. [使用.NET](http://使用.NET) 9開發一個ModbusTCP Master的庫。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.ModbusTcpMaster專案。
4. 請使用Minimal API不要用Controller。
5. 請在程式中加入簡易的英文註解。
6. 如有多種作法，請選擇Best Practice。
7. 平行化處理，在實際使用時提供可連線至至少1000台或以上Slave的能力。
8. 以IP跟PORT為一個端點，一個IP跟PORT的組合可能會有多個SLAVE，請使用一個連線就好，暫時不考慮同一個IP跟PORT號。
9. 如果是ModbusTCP的話，請加入「平行化」的設計，即可以同時收發多組訊息加快輪詢速度，此功能請提供開關接口。
10. 支援ModbusTCP及Modbus RTU over TCP的模式。
11. 支援Single Write跟Multiple Write的模式。
12. 要有能夠辨識及處理Slave回傳回來的錯誤碼的能力。
13. 實作所有Modbus協定的指令碼。
14. 符合Modbus的標準規範。
15. 可調整的Timeout及Gap時間。
16. 自己寫引擎，不要使用NModbus或相似的庫。
17. 請使用高性能的TCP管理機制。
18. 確保資源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
19. 需有一個完善的事件引擎，透過委派的方式，將數值更新、連線異常、CRC錯誤等等的事件，透過平行化的機制傳遞出去。
20. 連線與斷線重試的機制性能要強，同時也要避免用盡Socket的問題。
21. 提供FluentAPI接口。
22. 裝置的TCP連線需可以在外部管理，可以在實際引用這個引擎時，確認TCP連線的連線狀態等等。
23. 功能需要Thread-Safe。
24. 我會需要讀取狀態的暫存器清單，要自動分別轉成相對應的暫存器(如Coil/HoldingRegister等)合理的Modbus Command後輪循，不停取得最新狀態，避免過短或過長的暫存器請求範圍(請注意Modbus的暫存器長度限制)，提高效率，需要有可以設定的機制限制暫存器請求範圍。
25. 應該要可以Graceful Exit。