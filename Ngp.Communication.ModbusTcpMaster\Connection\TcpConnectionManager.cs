namespace Ngp.Communication.ModbusTcpMaster.Connection;

using System.Collections.Concurrent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Manages TCP connections to multiple Modbus devices
/// </summary>
public class TcpConnectionManager : IDisposable
{
    private readonly ConcurrentDictionary<string, ModbusConnection> _connections = new();
    private readonly object _lockObject = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;
    
    /// <summary>
    /// Default connection timeout
    /// </summary>
    public TimeSpan DefaultConnectionTimeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Default receive timeout
    /// </summary>
    public TimeSpan DefaultReceiveTimeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Maximum number of concurrent requests per connection
    /// </summary>
    public int MaxConcurrentRequestsPerConnection { get; set; } = 10;
    
    /// <summary>
    /// Maximum reconnection attempts
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = 3;
    
    /// <summary>
    /// Delay between reconnection attempts
    /// </summary>
    public TimeSpan ReconnectDelay { get; set; } = TimeSpan.FromSeconds(1);
    
    /// <summary>
    /// Idle timeout for connections
    /// </summary>
    public TimeSpan IdleTimeout { get; set; } = TimeSpan.FromMinutes(5);
    
    /// <summary>
    /// Cleanup interval for idle connections
    /// </summary>
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(1);
    
    /// <summary>
    /// Number of active connections
    /// </summary>
    public int ActiveConnectionCount => _connections.Count(kvp => kvp.Value.IsConnected);
    
    /// <summary>
    /// Total number of connections
    /// </summary>
    public int TotalConnectionCount => _connections.Count;
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Event raised when a communication error occurs
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
    
    public TcpConnectionManager()
    {
        // Start cleanup timer
        _cleanupTimer = new Timer(CleanupIdleConnections, null, CleanupInterval, CleanupInterval);
    }
    
    /// <summary>
    /// Get or create a connection to the specified endpoint
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <returns>Modbus connection</returns>
    public async Task<ModbusConnection> GetConnectionAsync(string ipAddress, int port)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(TcpConnectionManager));
        
        var key = $"{ipAddress}:{port}";
        
        if (_connections.TryGetValue(key, out var existingConnection))
        {
            if (existingConnection.IsConnected || existingConnection.Status == ConnectionStatus.Connecting)
            {
                return existingConnection;
            }
            
            // Remove failed connection
            _connections.TryRemove(key, out _);
            existingConnection.Dispose();
        }
        
        // Create new connection
        var connection = new ModbusConnection(ipAddress, port)
        {
            ConnectionTimeout = DefaultConnectionTimeout,
            ReceiveTimeout = DefaultReceiveTimeout,
            MaxConcurrentRequests = MaxConcurrentRequestsPerConnection,
            MaxReconnectAttempts = MaxReconnectAttempts,
            ReconnectDelay = ReconnectDelay
        };
        
        // Subscribe to events
        connection.StatusChanged += OnConnectionStatusChanged;
        connection.CommunicationError += OnCommunicationError;
        
        _connections[key] = connection;
        
        try
        {
            await connection.ConnectAsync(_cancellationTokenSource.Token);
            return connection;
        }
        catch
        {
            // Remove failed connection
            _connections.TryRemove(key, out _);
            connection.Dispose();
            throw;
        }
    }
    
    /// <summary>
    /// Send a request to the specified endpoint
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <param name="request">Modbus request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Modbus response</returns>
    public async Task<ModbusResponse> SendRequestAsync(string ipAddress, int port, ModbusRequest request, CancellationToken cancellationToken = default)
    {
        var connection = await GetConnectionAsync(ipAddress, port);
        return await connection.SendRequestAsync(request, cancellationToken);
    }
    
    /// <summary>
    /// Get connection status for the specified endpoint
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <returns>Connection status</returns>
    public ConnectionStatus GetConnectionStatus(string ipAddress, int port)
    {
        var key = $"{ipAddress}:{port}";
        
        if (_connections.TryGetValue(key, out var connection))
        {
            return connection.Status;
        }
        
        return ConnectionStatus.Disconnected;
    }
    
    /// <summary>
    /// Disconnect from the specified endpoint
    /// </summary>
    /// <param name="ipAddress">IP address of the device</param>
    /// <param name="port">Port number</param>
    /// <returns>Task representing the disconnection operation</returns>
    public async Task DisconnectAsync(string ipAddress, int port)
    {
        var key = $"{ipAddress}:{port}";
        
        if (_connections.TryRemove(key, out var connection))
        {
            await connection.DisconnectAsync();
            connection.Dispose();
        }
    }
    
    /// <summary>
    /// Disconnect all connections
    /// </summary>
    /// <returns>Task representing the disconnection operation</returns>
    public async Task DisconnectAllAsync()
    {
        var connections = _connections.Values.ToArray();
        _connections.Clear();
        
        var tasks = connections.Select(async connection =>
        {
            try
            {
                await connection.DisconnectAsync();
            }
            finally
            {
                connection.Dispose();
            }
        });
        
        await Task.WhenAll(tasks);
    }
    
    /// <summary>
    /// Get connection information for all endpoints
    /// </summary>
    /// <returns>Collection of connection information</returns>
    public IEnumerable<ConnectionInfo> GetConnectionInfo()
    {
        return _connections.Select(kvp => new ConnectionInfo
        {
            IpAddress = kvp.Value.IpAddress,
            Port = kvp.Value.Port,
            Status = kvp.Value.Status,
            LastActivity = kvp.Value.LastActivity,
            PendingRequestCount = kvp.Value.PendingRequestCount
        });
    }
    
    /// <summary>
    /// Handle connection status change events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        ConnectionStatusChanged?.Invoke(this, e);
    }
    
    /// <summary>
    /// Handle communication error events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnCommunicationError(object? sender, CommunicationErrorEventArgs e)
    {
        CommunicationError?.Invoke(this, e);
    }
    
    /// <summary>
    /// Clean up idle connections
    /// </summary>
    /// <param name="state">Timer state</param>
    private void CleanupIdleConnections(object? state)
    {
        if (_disposed)
            return;
        
        var now = DateTime.UtcNow;
        var connectionsToRemove = new List<string>();
        
        foreach (var kvp in _connections)
        {
            var connection = kvp.Value;
            
            if (connection.Status == ConnectionStatus.Failed ||
                (connection.Status == ConnectionStatus.Disconnected && 
                 now - connection.LastActivity > IdleTimeout))
            {
                connectionsToRemove.Add(kvp.Key);
            }
        }
        
        foreach (var key in connectionsToRemove)
        {
            if (_connections.TryRemove(key, out var connection))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await connection.DisconnectAsync();
                    }
                    finally
                    {
                        connection.Dispose();
                    }
                });
            }
        }
    }
    
    /// <summary>
    /// Dispose of the connection manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;
        
        _disposed = true;
        
        _cancellationTokenSource.Cancel();
        _cleanupTimer.Dispose();
        
        // Dispose all connections
        var connections = _connections.Values.ToArray();
        _connections.Clear();
        
        foreach (var connection in connections)
        {
            try
            {
                connection.DisconnectAsync().Wait(TimeSpan.FromSeconds(1));
            }
            catch
            {
                // Ignore disposal errors
            }
            finally
            {
                connection.Dispose();
            }
        }
        
        _cancellationTokenSource.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Connection information
/// </summary>
public class ConnectionInfo
{
    /// <summary>
    /// IP address of the device
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// Port number
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// Connection status
    /// </summary>
    public ConnectionStatus Status { get; set; }
    
    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime LastActivity { get; set; }
    
    /// <summary>
    /// Number of pending requests
    /// </summary>
    public int PendingRequestCount { get; set; }
}
