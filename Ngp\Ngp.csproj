<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>4610a975-a092-4e9f-80c9-fd3ec77e9a20</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ngp.ServiceDefaults\Ngp.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Ngp.Communication.ModbusTcpMaster\Ngp.Communication.ModbusTcpMaster.csproj" />
  </ItemGroup>

</Project>
