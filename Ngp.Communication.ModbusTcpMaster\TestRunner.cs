namespace Ngp.Communication.ModbusTcpMaster;

using Ngp.Communication.ModbusTcpMaster.Tests;

/// <summary>
/// Simple test runner for ModbusTcpMaster library
/// </summary>
public class TestRunner
{
    /// <summary>
    /// Main entry point for running tests
    /// </summary>
    public static async Task Main(string[] args)
    {
        Console.WriteLine("Ngp.Communication.ModbusTcpMaster Test Runner");
        Console.WriteLine("============================================");
        Console.WriteLine();
        
        try
        {
            await ModbusMasterTests.RunAllTests();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test execution failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
