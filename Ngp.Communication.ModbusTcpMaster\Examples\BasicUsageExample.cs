namespace Ngp.Communication.ModbusTcpMaster.Examples;

using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Example demonstrating basic usage of the Modbus TCP Master library
/// </summary>
public class BasicUsageExample
{
    /// <summary>
    /// Example of basic read/write operations
    /// </summary>
    public static async Task BasicReadWriteExample()
    {
        // Create a ModbusMaster using the fluent API
        using var modbusMaster = new ModbusMasterBuilder()
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithDefaultTimeout(TimeSpan.FromSeconds(5))
            .WithParallelProcessing(true)
            .WithMaxParallelRequests(50)
            .OnDataValueUpdated((sender, e) =>
            {
                Console.WriteLine($"Data updated: {e.RegisterType} at {e.StartAddress}");
            })
            .OnConnectionStatusChanged((sender, e) =>
            {
                Console.WriteLine($"Connection {e.IpAddress}:{e.Port} status: {e.CurrentStatus}");
            })
            .Build();

        try
        {
            // Start the master
            await modbusMaster.StartAsync();

            var deviceIp = "*************";
            var devicePort = 502;
            byte unitId = 1;

            // Read holding registers
            Console.WriteLine("Reading holding registers...");
            var holdingRegisters = await modbusMaster.ReadHoldingRegistersAsync(
                deviceIp, devicePort, unitId, startAddress: 0, quantity: 10);
            
            Console.WriteLine($"Read {holdingRegisters.Length} holding registers:");
            for (int i = 0; i < holdingRegisters.Length; i++)
            {
                Console.WriteLine($"  Register {i}: {holdingRegisters[i]}");
            }

            // Read coils
            Console.WriteLine("\nReading coils...");
            var coils = await modbusMaster.ReadCoilsAsync(
                deviceIp, devicePort, unitId, startAddress: 0, quantity: 16);
            
            Console.WriteLine($"Read {coils.Length} coils:");
            for (int i = 0; i < coils.Length; i++)
            {
                Console.WriteLine($"  Coil {i}: {coils[i]}");
            }

            // Write a single register
            Console.WriteLine("\nWriting single register...");
            await modbusMaster.WriteSingleRegisterAsync(
                deviceIp, devicePort, unitId, address: 0, value: 1234);
            Console.WriteLine("Single register written successfully");

            // Write multiple registers
            Console.WriteLine("\nWriting multiple registers...");
            var values = new ushort[] { 100, 200, 300, 400, 500 };
            await modbusMaster.WriteMultipleRegistersAsync(
                deviceIp, devicePort, unitId, startAddress: 10, values);
            Console.WriteLine($"Written {values.Length} registers successfully");

            // Write a single coil
            Console.WriteLine("\nWriting single coil...");
            await modbusMaster.WriteSingleCoilAsync(
                deviceIp, devicePort, unitId, address: 0, value: true);
            Console.WriteLine("Single coil written successfully");

            // Write multiple coils
            Console.WriteLine("\nWriting multiple coils...");
            var coilValues = new bool[] { true, false, true, false, true };
            await modbusMaster.WriteMultipleCoilsAsync(
                deviceIp, devicePort, unitId, startAddress: 10, coilValues);
            Console.WriteLine($"Written {coilValues.Length} coils successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            // Stop the master
            await modbusMaster.StopAsync();
        }
    }

    /// <summary>
    /// Example of automatic polling configuration
    /// </summary>
    public static async Task PollingExample()
    {
        // Create a ModbusMaster with polling configuration
        using var modbusMaster = new ModbusMasterBuilder()
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithPollingSettings(
                pollingCheckInterval: TimeSpan.FromMilliseconds(100),
                maxConcurrentPolling: 20,
                enableChangeDetection: true)
            .AddDevice("*************", 502)
                .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 10, 
                    pollingInterval: TimeSpan.FromSeconds(1))
                .PollCoils(unitId: 1, startAddress: 0, count: 16, 
                    pollingInterval: TimeSpan.FromSeconds(2))
                .PollInputRegisters(unitId: 1, startAddress: 100, count: 5, 
                    pollingInterval: TimeSpan.FromMilliseconds(500))
                .EndDevice()
            .AddDevice("*************", 502)
                .PollHoldingRegisters(unitId: 2, startAddress: 0, count: 20, 
                    pollingInterval: TimeSpan.FromSeconds(1))
                .EndDevice()
            .OnDataValueUpdated((sender, e) =>
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] Data updated: " +
                    $"{e.IpAddress}:{e.Port} Unit:{e.UnitId} {e.RegisterType} " +
                    $"Address:{e.StartAddress} Count:{(e.RegisterValues?.Length ?? e.CoilValues?.Length ?? 0)}");
            })
            .OnConnectionStatusChanged((sender, e) =>
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] Connection status: " +
                    $"{e.IpAddress}:{e.Port} {e.PreviousStatus} -> {e.CurrentStatus}");
            })
            .OnCommunicationError((sender, e) =>
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] Communication error: " +
                    $"{e.IpAddress}:{e.Port} {e.ErrorType} - {e.ErrorMessage}");
            })
            .Build();

        try
        {
            // Start polling
            Console.WriteLine("Starting automatic polling...");
            await modbusMaster.StartAsync();

            // Let it run for 30 seconds
            Console.WriteLine("Polling for 30 seconds... Press any key to stop early.");
            var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            
            while (!cancellationTokenSource.Token.IsCancellationRequested)
            {
                if (Console.KeyAvailable)
                {
                    Console.ReadKey();
                    break;
                }
                await Task.Delay(100);
            }

            Console.WriteLine("\nStopping polling...");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            await modbusMaster.StopAsync();
        }
    }

    /// <summary>
    /// Example of high-performance parallel operations
    /// </summary>
    public static async Task HighPerformanceExample()
    {
        using var modbusMaster = new ModbusMasterBuilder()
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithParallelProcessing(true)
            .WithMaxParallelRequests(100)
            .WithConnectionSettings(
                connectionTimeout: TimeSpan.FromSeconds(2),
                receiveTimeout: TimeSpan.FromSeconds(2),
                maxConcurrentRequestsPerConnection: 20)
            .Build();

        try
        {
            await modbusMaster.StartAsync();

            var deviceIp = "*************";
            var devicePort = 502;
            byte unitId = 1;

            // Perform multiple parallel read operations
            Console.WriteLine("Performing parallel read operations...");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            var tasks = new List<Task>();
            
            // Create 50 parallel read tasks
            for (int i = 0; i < 50; i++)
            {
                var startAddress = (ushort)(i * 10);
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        var registers = await modbusMaster.ReadHoldingRegistersAsync(
                            deviceIp, devicePort, unitId, startAddress, 10);
                        Console.WriteLine($"Read completed for address {startAddress}: {registers.Length} registers");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Read failed for address {startAddress}: {ex.Message}");
                    }
                }));
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            Console.WriteLine($"Completed 50 parallel reads in {stopwatch.ElapsedMilliseconds}ms");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            await modbusMaster.StopAsync();
        }
    }

    /// <summary>
    /// Example of connection management
    /// </summary>
    public static async Task ConnectionManagementExample()
    {
        using var modbusMaster = new ModbusMasterBuilder()
            .WithConnectionSettings(
                connectionTimeout: TimeSpan.FromSeconds(5),
                maxReconnectAttempts: 5,
                reconnectDelay: TimeSpan.FromSeconds(2))
            .OnConnectionStatusChanged((sender, e) =>
            {
                Console.WriteLine($"Connection {e.IpAddress}:{e.Port}: {e.PreviousStatus} -> {e.CurrentStatus}");
                if (!string.IsNullOrEmpty(e.ErrorMessage))
                {
                    Console.WriteLine($"  Error: {e.ErrorMessage}");
                }
            })
            .Build();

        try
        {
            await modbusMaster.StartAsync();

            // Get connection information
            Console.WriteLine("Current connections:");
            var connections = modbusMaster.ConnectionManager.GetConnectionInfo();
            foreach (var conn in connections)
            {
                Console.WriteLine($"  {conn.IpAddress}:{conn.Port} - Status: {conn.Status}, " +
                    $"Last Activity: {conn.LastActivity}, Pending: {conn.PendingRequestCount}");
            }

            // Try to connect to multiple devices
            var devices = new[]
            {
                ("*************", 502),
                ("*************", 502),
                ("*************", 502)
            };

            foreach (var (ip, port) in devices)
            {
                try
                {
                    Console.WriteLine($"Attempting to read from {ip}:{port}...");
                    var registers = await modbusMaster.ReadHoldingRegistersAsync(ip, port, 1, 0, 1);
                    Console.WriteLine($"Successfully read from {ip}:{port}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to read from {ip}:{port}: {ex.Message}");
                }
            }

            // Show final connection status
            Console.WriteLine("\nFinal connection status:");
            connections = modbusMaster.ConnectionManager.GetConnectionInfo();
            foreach (var conn in connections)
            {
                Console.WriteLine($"  {conn.IpAddress}:{conn.Port} - Status: {conn.Status}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            await modbusMaster.StopAsync();
        }
    }
}
