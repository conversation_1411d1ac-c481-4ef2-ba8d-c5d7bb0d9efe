namespace Ngp.Communication.ModbusTcpMaster.Fluent;

using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Fluent API builder for ModbusMaster
/// </summary>
public class ModbusMasterBuilder
{
    private readonly ModbusMaster _modbusMaster;
    private readonly List<DeviceConfiguration> _deviceConfigurations = new();
    
    /// <summary>
    /// Initialize a new ModbusMasterBuilder
    /// </summary>
    public ModbusMasterBuilder()
    {
        _modbusMaster = new ModbusMaster();
    }
    
    /// <summary>
    /// Set the protocol mode
    /// </summary>
    /// <param name="mode">Protocol mode</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithProtocolMode(ModbusProtocolMode mode)
    {
        _modbusMaster.ProtocolMode = mode;
        return this;
    }
    
    /// <summary>
    /// Set the default timeout
    /// </summary>
    /// <param name="timeout">Default timeout</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithDefaultTimeout(TimeSpan timeout)
    {
        _modbusMaster.DefaultTimeout = timeout;
        return this;
    }
    
    /// <summary>
    /// Set the gap time between requests
    /// </summary>
    /// <param name="gapTime">Gap time</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithGapTime(TimeSpan gapTime)
    {
        _modbusMaster.GapTime = gapTime;
        return this;
    }
    
    /// <summary>
    /// Enable or disable parallel processing
    /// </summary>
    /// <param name="enabled">Enable parallel processing</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithParallelProcessing(bool enabled = true)
    {
        _modbusMaster.EnableParallelProcessing = enabled;
        return this;
    }
    
    /// <summary>
    /// Set the maximum number of parallel requests
    /// </summary>
    /// <param name="maxRequests">Maximum parallel requests</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithMaxParallelRequests(int maxRequests)
    {
        _modbusMaster.MaxParallelRequests = maxRequests;
        return this;
    }
    
    /// <summary>
    /// Configure connection settings
    /// </summary>
    /// <param name="connectionTimeout">Connection timeout</param>
    /// <param name="receiveTimeout">Receive timeout</param>
    /// <param name="maxConcurrentRequestsPerConnection">Max concurrent requests per connection</param>
    /// <param name="maxReconnectAttempts">Max reconnection attempts</param>
    /// <param name="reconnectDelay">Reconnection delay</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithConnectionSettings(
        TimeSpan? connectionTimeout = null,
        TimeSpan? receiveTimeout = null,
        int? maxConcurrentRequestsPerConnection = null,
        int? maxReconnectAttempts = null,
        TimeSpan? reconnectDelay = null)
    {
        var connectionManager = _modbusMaster.ConnectionManager;
        
        if (connectionTimeout.HasValue)
            connectionManager.DefaultConnectionTimeout = connectionTimeout.Value;
        
        if (receiveTimeout.HasValue)
            connectionManager.DefaultReceiveTimeout = receiveTimeout.Value;
        
        if (maxConcurrentRequestsPerConnection.HasValue)
            connectionManager.MaxConcurrentRequestsPerConnection = maxConcurrentRequestsPerConnection.Value;
        
        if (maxReconnectAttempts.HasValue)
            connectionManager.MaxReconnectAttempts = maxReconnectAttempts.Value;
        
        if (reconnectDelay.HasValue)
            connectionManager.ReconnectDelay = reconnectDelay.Value;
        
        return this;
    }
    
    /// <summary>
    /// Configure polling settings
    /// </summary>
    /// <param name="pollingCheckInterval">Polling check interval</param>
    /// <param name="maxConcurrentPolling">Max concurrent polling operations</param>
    /// <param name="enableChangeDetection">Enable change detection</param>
    /// <param name="maxRegisterRangeSize">Max register range size</param>
    /// <param name="maxCoilRangeSize">Max coil range size</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithPollingSettings(
        TimeSpan? pollingCheckInterval = null,
        int? maxConcurrentPolling = null,
        bool? enableChangeDetection = null,
        ushort? maxRegisterRangeSize = null,
        ushort? maxCoilRangeSize = null)
    {
        var pollingEngine = _modbusMaster.PollingEngine;
        
        if (pollingCheckInterval.HasValue)
            pollingEngine.PollingCheckInterval = pollingCheckInterval.Value;
        
        if (maxConcurrentPolling.HasValue)
            pollingEngine.MaxConcurrentPolling = maxConcurrentPolling.Value;
        
        if (enableChangeDetection.HasValue)
            pollingEngine.EnableChangeDetection = enableChangeDetection.Value;
        
        if (maxRegisterRangeSize.HasValue)
            pollingEngine.MaxRegisterRangeSize = maxRegisterRangeSize.Value;
        
        if (maxCoilRangeSize.HasValue)
            pollingEngine.MaxCoilRangeSize = maxCoilRangeSize.Value;
        
        return this;
    }
    
    /// <summary>
    /// Add a device configuration
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder AddDevice(string ipAddress, int port = 502)
    {
        return new DeviceConfigurationBuilder(this, ipAddress, port);
    }
    
    /// <summary>
    /// Subscribe to data value updated events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnDataValueUpdated(EventHandler<DataValueUpdatedEventArgs> handler)
    {
        _modbusMaster.DataValueUpdated += handler;
        return this;
    }
    
    /// <summary>
    /// Subscribe to connection status changed events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnConnectionStatusChanged(EventHandler<ConnectionStatusChangedEventArgs> handler)
    {
        _modbusMaster.ConnectionStatusChanged += handler;
        return this;
    }
    
    /// <summary>
    /// Subscribe to Modbus exception events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnModbusException(EventHandler<ModbusExceptionEventArgs> handler)
    {
        _modbusMaster.ModbusExceptionOccurred += handler;
        return this;
    }
    
    /// <summary>
    /// Subscribe to communication error events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnCommunicationError(EventHandler<CommunicationErrorEventArgs> handler)
    {
        _modbusMaster.CommunicationError += handler;
        return this;
    }
    
    /// <summary>
    /// Subscribe to timeout events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnTimeout(EventHandler<TimeoutEventArgs> handler)
    {
        _modbusMaster.TimeoutOccurred += handler;
        return this;
    }
    
    /// <summary>
    /// Build and configure the ModbusMaster
    /// </summary>
    /// <returns>Configured ModbusMaster instance</returns>
    public ModbusMaster Build()
    {
        // Apply device configurations
        foreach (var deviceConfig in _deviceConfigurations)
        {
            foreach (var range in deviceConfig.RegisterRanges)
            {
                _modbusMaster.AddPollingRange(deviceConfig.IpAddress, deviceConfig.Port, range);
            }
        }
        
        return _modbusMaster;
    }
    
    /// <summary>
    /// Internal method to add device configuration
    /// </summary>
    /// <param name="deviceConfig">Device configuration</param>
    internal void AddDeviceConfiguration(DeviceConfiguration deviceConfig)
    {
        _deviceConfigurations.Add(deviceConfig);
    }
}

/// <summary>
/// Device configuration builder
/// </summary>
public class DeviceConfigurationBuilder
{
    private readonly ModbusMasterBuilder _parentBuilder;
    private readonly DeviceConfiguration _deviceConfig;
    
    internal DeviceConfigurationBuilder(ModbusMasterBuilder parentBuilder, string ipAddress, int port)
    {
        _parentBuilder = parentBuilder;
        _deviceConfig = new DeviceConfiguration
        {
            IpAddress = ipAddress,
            Port = port
        };
    }
    
    /// <summary>
    /// Add a coil polling range
    /// </summary>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of coils</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollCoils(byte unitId, ushort startAddress, ushort count, TimeSpan? pollingInterval = null)
    {
        _deviceConfig.RegisterRanges.Add(new RegisterRange
        {
            RegisterType = ModbusRegisterType.Coil,
            UnitId = unitId,
            StartAddress = startAddress,
            Count = count,
            PollingInterval = pollingInterval ?? TimeSpan.FromSeconds(1)
        });
        
        return this;
    }
    
    /// <summary>
    /// Add a discrete input polling range
    /// </summary>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of discrete inputs</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollDiscreteInputs(byte unitId, ushort startAddress, ushort count, TimeSpan? pollingInterval = null)
    {
        _deviceConfig.RegisterRanges.Add(new RegisterRange
        {
            RegisterType = ModbusRegisterType.DiscreteInput,
            UnitId = unitId,
            StartAddress = startAddress,
            Count = count,
            PollingInterval = pollingInterval ?? TimeSpan.FromSeconds(1)
        });
        
        return this;
    }
    
    /// <summary>
    /// Add a holding register polling range
    /// </summary>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollHoldingRegisters(byte unitId, ushort startAddress, ushort count, TimeSpan? pollingInterval = null)
    {
        _deviceConfig.RegisterRanges.Add(new RegisterRange
        {
            RegisterType = ModbusRegisterType.HoldingRegister,
            UnitId = unitId,
            StartAddress = startAddress,
            Count = count,
            PollingInterval = pollingInterval ?? TimeSpan.FromSeconds(1)
        });
        
        return this;
    }
    
    /// <summary>
    /// Add an input register polling range
    /// </summary>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollInputRegisters(byte unitId, ushort startAddress, ushort count, TimeSpan? pollingInterval = null)
    {
        _deviceConfig.RegisterRanges.Add(new RegisterRange
        {
            RegisterType = ModbusRegisterType.InputRegister,
            UnitId = unitId,
            StartAddress = startAddress,
            Count = count,
            PollingInterval = pollingInterval ?? TimeSpan.FromSeconds(1)
        });
        
        return this;
    }
    
    /// <summary>
    /// Complete device configuration and return to parent builder
    /// </summary>
    /// <returns>Parent ModbusMasterBuilder</returns>
    public ModbusMasterBuilder EndDevice()
    {
        _parentBuilder.AddDeviceConfiguration(_deviceConfig);
        return _parentBuilder;
    }
}

/// <summary>
/// Device configuration
/// </summary>
internal class DeviceConfiguration
{
    public string IpAddress { get; set; } = string.Empty;
    public int Port { get; set; }
    public List<RegisterRange> RegisterRanges { get; set; } = new();
}
