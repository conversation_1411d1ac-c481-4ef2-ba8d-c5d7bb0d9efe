namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a Modbus request
/// </summary>
public class ModbusRequest
{
    /// <summary>
    /// Transaction identifier for Modbus TCP
    /// </summary>
    public ushort TransactionId { get; set; }
    
    /// <summary>
    /// Protocol identifier (0 for Modbus)
    /// </summary>
    public ushort ProtocolId { get; set; } = 0;
    
    /// <summary>
    /// Length of the following bytes
    /// </summary>
    public ushort Length { get; set; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }
    
    /// <summary>
    /// Function code
    /// </summary>
    public ModbusFunctionCode FunctionCode { get; set; }
    
    /// <summary>
    /// Starting address
    /// </summary>
    public ushort StartingAddress { get; set; }
    
    /// <summary>
    /// Quantity of registers/coils
    /// </summary>
    public ushort Quantity { get; set; }
    
    /// <summary>
    /// Data bytes for write operations
    /// </summary>
    public byte[]? Data { get; set; }
    
    /// <summary>
    /// Timestamp when the request was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Timeout for this specific request
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Convert the request to byte array for transmission
    /// </summary>
    /// <returns>Byte array representation of the request</returns>
    public byte[] ToByteArray()
    {
        var dataLength = Data?.Length ?? 0;
        var pduLength = 2 + dataLength; // Function code + address + quantity/data
        
        if (FunctionCode == ModbusFunctionCode.ReadCoils ||
            FunctionCode == ModbusFunctionCode.ReadDiscreteInputs ||
            FunctionCode == ModbusFunctionCode.ReadHoldingRegisters ||
            FunctionCode == ModbusFunctionCode.ReadInputRegisters)
        {
            pduLength = 6; // Function code + starting address + quantity
        }
        else if (FunctionCode == ModbusFunctionCode.WriteSingleCoil ||
                 FunctionCode == ModbusFunctionCode.WriteSingleRegister)
        {
            pduLength = 6; // Function code + address + value
        }
        else if (FunctionCode == ModbusFunctionCode.WriteMultipleCoils ||
                 FunctionCode == ModbusFunctionCode.WriteMultipleRegisters)
        {
            pduLength = 7 + dataLength; // Function code + starting address + quantity + byte count + data
        }
        
        Length = (ushort)(pduLength + 1); // +1 for unit identifier
        
        var buffer = new List<byte>();
        
        // MBAP Header
        buffer.AddRange(BitConverter.GetBytes(TransactionId).Reverse());
        buffer.AddRange(BitConverter.GetBytes(ProtocolId).Reverse());
        buffer.AddRange(BitConverter.GetBytes(Length).Reverse());
        buffer.Add(UnitId);
        
        // PDU
        buffer.Add((byte)FunctionCode);
        
        if (FunctionCode == ModbusFunctionCode.ReadCoils ||
            FunctionCode == ModbusFunctionCode.ReadDiscreteInputs ||
            FunctionCode == ModbusFunctionCode.ReadHoldingRegisters ||
            FunctionCode == ModbusFunctionCode.ReadInputRegisters)
        {
            buffer.AddRange(BitConverter.GetBytes(StartingAddress).Reverse());
            buffer.AddRange(BitConverter.GetBytes(Quantity).Reverse());
        }
        else if (FunctionCode == ModbusFunctionCode.WriteSingleCoil)
        {
            buffer.AddRange(BitConverter.GetBytes(StartingAddress).Reverse());
            buffer.AddRange(BitConverter.GetBytes((ushort)(Data?[0] == 1 ? 0xFF00 : 0x0000)).Reverse());
        }
        else if (FunctionCode == ModbusFunctionCode.WriteSingleRegister)
        {
            buffer.AddRange(BitConverter.GetBytes(StartingAddress).Reverse());
            if (Data != null && Data.Length >= 2)
            {
                buffer.AddRange(new[] { Data[0], Data[1] });
            }
        }
        else if (FunctionCode == ModbusFunctionCode.WriteMultipleCoils ||
                 FunctionCode == ModbusFunctionCode.WriteMultipleRegisters)
        {
            buffer.AddRange(BitConverter.GetBytes(StartingAddress).Reverse());
            buffer.AddRange(BitConverter.GetBytes(Quantity).Reverse());
            buffer.Add((byte)dataLength);
            if (Data != null)
            {
                buffer.AddRange(Data);
            }
        }
        
        return buffer.ToArray();
    }
}
