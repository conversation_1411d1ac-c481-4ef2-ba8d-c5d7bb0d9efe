namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Modbus function codes as defined in the Modbus specification
/// </summary>
public enum ModbusFunctionCode : byte
{
    // Read functions
    ReadCoils = 0x01,
    ReadDiscreteInputs = 0x02,
    ReadHoldingRegisters = 0x03,
    ReadInputRegisters = 0x04,
    
    // Write functions
    WriteSingleCoil = 0x05,
    WriteSingleRegister = 0x06,
    WriteMultipleCoils = 0x0F,
    WriteMultipleRegisters = 0x10,
    
    // Diagnostic functions
    ReadExceptionStatus = 0x07,
    Diagnostics = 0x08,
    GetCommEventCounter = 0x0B,
    GetCommEventLog = 0x0C,
    ReportSlaveId = 0x11,
    
    // File record functions
    ReadFileRecord = 0x14,
    WriteFileRecord = 0x15,
    MaskWriteRegister = 0x16,
    ReadWriteMultipleRegisters = 0x17,
    ReadFifoQueue = 0x18,
    
    // Encapsulated interface transport
    EncapsulatedInterfaceTransport = 0x2B
}

/// <summary>
/// Modbus exception codes
/// </summary>
public enum ModbusExceptionCode : byte
{
    IllegalFunction = 0x01,
    IllegalDataAddress = 0x02,
    IllegalDataValue = 0x03,
    SlaveDeviceFailure = 0x04,
    Acknowledge = 0x05,
    SlaveDeviceBusy = 0x06,
    MemoryParityError = 0x08,
    GatewayPathUnavailable = 0x0A,
    GatewayTargetDeviceFailedToRespond = 0x0B
}

/// <summary>
/// Modbus register types
/// </summary>
public enum ModbusRegisterType
{
    Coil,
    DiscreteInput,
    InputRegister,
    HoldingRegister
}

/// <summary>
/// Connection status enumeration
/// </summary>
public enum ConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Failed
}

/// <summary>
/// Modbus protocol mode
/// </summary>
public enum ModbusProtocolMode
{
    ModbusTcp,
    ModbusRtuOverTcp
}
