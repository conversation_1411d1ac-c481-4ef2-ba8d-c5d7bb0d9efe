namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a range of Modbus registers for polling
/// </summary>
public class RegisterRange
{
    /// <summary>
    /// Type of register
    /// </summary>
    public ModbusRegisterType RegisterType { get; set; }
    
    /// <summary>
    /// Starting address of the range
    /// </summary>
    public ushort StartAddress { get; set; }
    
    /// <summary>
    /// Number of registers/coils in the range
    /// </summary>
    public ushort Count { get; set; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }
    
    /// <summary>
    /// Polling interval for this range
    /// </summary>
    public TimeSpan PollingInterval { get; set; } = TimeSpan.FromSeconds(1);
    
    /// <summary>
    /// Last time this range was polled
    /// </summary>
    public DateTime LastPolled { get; set; } = DateTime.MinValue;
    
    /// <summary>
    /// Indicates if this range is currently being polled
    /// </summary>
    public bool IsPolling { get; set; }
    
    /// <summary>
    /// Maximum number of registers that can be read in a single request
    /// </summary>
    public const ushort MaxRegisterCount = 125;
    
    /// <summary>
    /// Maximum number of coils that can be read in a single request
    /// </summary>
    public const ushort MaxCoilCount = 2000;
    
    /// <summary>
    /// Split this range into smaller ranges if it exceeds the maximum allowed size
    /// </summary>
    /// <returns>Collection of register ranges</returns>
    public IEnumerable<RegisterRange> SplitToOptimalRanges()
    {
        var maxCount = RegisterType == ModbusRegisterType.Coil || 
                      RegisterType == ModbusRegisterType.DiscreteInput 
                      ? MaxCoilCount : MaxRegisterCount;
        
        if (Count <= maxCount)
        {
            yield return this;
            yield break;
        }
        
        var currentAddress = StartAddress;
        var remainingCount = Count;
        
        while (remainingCount > 0)
        {
            var currentCount = Math.Min(remainingCount, maxCount);
            
            yield return new RegisterRange
            {
                RegisterType = RegisterType,
                StartAddress = currentAddress,
                Count = (ushort)currentCount,
                UnitId = UnitId,
                PollingInterval = PollingInterval
            };
            
            currentAddress += (ushort)currentCount;
            remainingCount -= (ushort)currentCount;
        }
    }
    
    /// <summary>
    /// Get the function code for reading this register type
    /// </summary>
    /// <returns>Appropriate function code</returns>
    public ModbusFunctionCode GetReadFunctionCode()
    {
        return RegisterType switch
        {
            ModbusRegisterType.Coil => ModbusFunctionCode.ReadCoils,
            ModbusRegisterType.DiscreteInput => ModbusFunctionCode.ReadDiscreteInputs,
            ModbusRegisterType.InputRegister => ModbusFunctionCode.ReadInputRegisters,
            ModbusRegisterType.HoldingRegister => ModbusFunctionCode.ReadHoldingRegisters,
            _ => throw new ArgumentException($"Unknown register type: {RegisterType}")
        };
    }
    
    /// <summary>
    /// Check if it's time to poll this range
    /// </summary>
    /// <returns>True if polling is due</returns>
    public bool IsPollingDue()
    {
        return DateTime.UtcNow - LastPolled >= PollingInterval && !IsPolling;
    }
    
    /// <summary>
    /// Create a unique identifier for this range
    /// </summary>
    /// <returns>String identifier</returns>
    public string GetIdentifier()
    {
        return $"{UnitId}:{RegisterType}:{StartAddress}:{Count}";
    }
    
    public override string ToString()
    {
        return $"{RegisterType} Unit:{UnitId} Address:{StartAddress} Count:{Count}";
    }
    
    public override bool Equals(object? obj)
    {
        if (obj is not RegisterRange other) return false;
        
        return RegisterType == other.RegisterType &&
               StartAddress == other.StartAddress &&
               Count == other.Count &&
               UnitId == other.UnitId;
    }
    
    public override int GetHashCode()
    {
        return HashCode.Combine(RegisterType, StartAddress, Count, UnitId);
    }
}
