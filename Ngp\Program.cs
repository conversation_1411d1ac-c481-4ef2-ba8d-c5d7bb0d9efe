using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Register ModbusMaster as singleton
builder.Services.AddSingleton<ModbusMaster>(serviceProvider =>
{
    var modbusMaster = new ModbusMasterBuilder()
        .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
        .WithDefaultTimeout(TimeSpan.FromSeconds(5))
        .WithGapTime(TimeSpan.FromMilliseconds(10))
        .WithParallelProcessing(true)
        .WithMaxParallelRequests(100)
        .WithConnectionSettings(
            connectionTimeout: TimeSpan.FromSeconds(5),
            receiveTimeout: TimeSpan.FromSeconds(5),
            maxConcurrentRequestsPerConnection: 10,
            maxReconnectAttempts: 3,
            reconnectDelay: TimeSpan.FromSeconds(1))
        .WithPollingSettings(
            pollingCheckInterval: TimeSpan.FromMilliseconds(100),
            maxConcurrentPolling: 50,
            enableChangeDetection: true,
            maxRegisterRangeSize: 100,
            maxCoilRangeSize: 1000)
        .OnDataValueUpdated((sender, e) =>
        {
            // Log data value updates
            Console.WriteLine($"Data updated: {e.IpAddress}:{e.Port} Unit:{e.UnitId} {e.RegisterType} Address:{e.StartAddress}");
        })
        .OnConnectionStatusChanged((sender, e) =>
        {
            // Log connection status changes
            Console.WriteLine($"Connection status changed: {e.IpAddress}:{e.Port} {e.PreviousStatus} -> {e.CurrentStatus}");
        })
        .OnCommunicationError((sender, e) =>
        {
            // Log communication errors
            Console.WriteLine($"Communication error: {e.IpAddress}:{e.Port} {e.ErrorType} - {e.ErrorMessage}");
        })
        .Build();

    return modbusMaster;
});

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

// Start ModbusMaster on application startup
var modbusMaster = app.Services.GetRequiredService<ModbusMaster>();
await modbusMaster.StartAsync();

// Graceful shutdown
app.Lifetime.ApplicationStopping.Register(async () =>
{
    await modbusMaster.StopAsync();
    modbusMaster.Dispose();
});

// Modbus TCP Master API endpoints
var modbusApi = app.MapGroup("/modbus").WithTags("Modbus TCP Master");

// Read coils
modbusApi.MapGet("/coils", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadCoilsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadCoils")
.WithSummary("Read coils from a Modbus device");

// Read discrete inputs
modbusApi.MapGet("/discrete-inputs", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadDiscreteInputsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadDiscreteInputs")
.WithSummary("Read discrete inputs from a Modbus device");

// Read holding registers
modbusApi.MapGet("/holding-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadHoldingRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadHoldingRegisters")
.WithSummary("Read holding registers from a Modbus device");

// Read input registers
modbusApi.MapGet("/input-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadInputRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadInputRegisters")
.WithSummary("Read input registers from a Modbus device");

// Write single coil
modbusApi.MapPost("/coil", async (WriteCoilRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleCoilAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Coil written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleCoil")
.WithSummary("Write a single coil to a Modbus device");

// Write single register
modbusApi.MapPost("/register", async (WriteRegisterRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleRegisterAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Register written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleRegister")
.WithSummary("Write a single register to a Modbus device");

// Write multiple coils
modbusApi.MapPost("/coils", async (WriteMultipleCoilsRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleCoilsAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = "Coils written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleCoils")
.WithSummary("Write multiple coils to a Modbus device");

// Write multiple registers
modbusApi.MapPost("/registers", async (WriteMultipleRegistersRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleRegistersAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = "Registers written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleRegisters")
.WithSummary("Write multiple registers to a Modbus device");

// Add polling range
modbusApi.MapPost("/polling/add", (AddPollingRangeRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count,
            PollingInterval = TimeSpan.FromMilliseconds(request.PollingIntervalMs)
        };

        modbusMaster.AddPollingRange(request.Ip, request.Port, registerRange);
        return Results.Ok(new { success = true, message = "Polling range added successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("AddPollingRange")
.WithSummary("Add a register range for automatic polling");

// Remove polling range
modbusApi.MapPost("/polling/remove", (RemovePollingRangeRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count
        };

        modbusMaster.RemovePollingRange(request.Ip, request.Port, registerRange);
        return Results.Ok(new { success = true, message = "Polling range removed successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("RemovePollingRange")
.WithSummary("Remove a register range from automatic polling");

// Get connection status
modbusApi.MapGet("/connections", () =>
{
    try
    {
        var connections = modbusMaster.ConnectionManager.GetConnectionInfo();
        return Results.Ok(new { success = true, connections });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetConnections")
.WithSummary("Get status of all connections");

// Get polling ranges
modbusApi.MapGet("/polling", (string ip, int port) =>
{
    try
    {
        var ranges = modbusMaster.PollingEngine.GetPollingRanges(ip, port);
        return Results.Ok(new { success = true, ranges });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetPollingRanges")
.WithSummary("Get polling ranges for a device");

app.Run();

// Request models
record WriteCoilRequest(string Ip, int Port, byte UnitId, ushort Address, bool Value);
record WriteRegisterRequest(string Ip, int Port, byte UnitId, ushort Address, ushort Value);
record WriteMultipleCoilsRequest(string Ip, int Port, byte UnitId, ushort StartAddress, bool[] Values);
record WriteMultipleRegistersRequest(string Ip, int Port, byte UnitId, ushort StartAddress, ushort[] Values);
record AddPollingRangeRequest(string Ip, int Port, byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count, int PollingIntervalMs);
record RemovePollingRangeRequest(string Ip, int Port, byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count);
