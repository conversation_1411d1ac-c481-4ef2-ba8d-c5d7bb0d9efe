namespace Ngp.Communication.ModbusTcpMaster.Tests;

using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Exceptions;

/// <summary>
/// Unit tests for ModbusMaster functionality
/// </summary>
public class ModbusMasterTests
{
    /// <summary>
    /// Test ModbusMaster creation using FluentAPI
    /// </summary>
    public static void TestModbusMasterCreation()
    {
        Console.WriteLine("Testing ModbusMaster creation...");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
                .WithDefaultTimeout(TimeSpan.FromSeconds(5))
                .WithParallelProcessing(true)
                .WithMaxParallelRequests(50)
                .Build();
            
            Console.WriteLine("✓ ModbusMaster created successfully");
            Console.WriteLine($"  Protocol Mode: {modbusMaster.ProtocolMode}");
            Console.WriteLine($"  Default Timeout: {modbusMaster.DefaultTimeout}");
            Console.WriteLine($"  Parallel Processing: {modbusMaster.EnableParallelProcessing}");
            Console.WriteLine($"  Max Parallel Requests: {modbusMaster.MaxParallelRequests}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Failed to create ModbusMaster: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Test register range validation
    /// </summary>
    public static void TestRegisterRangeValidation()
    {
        Console.WriteLine("\nTesting register range validation...");
        
        try
        {
            // Test valid range
            var validRange = new RegisterRange
            {
                RegisterType = ModbusRegisterType.HoldingRegister,
                UnitId = 1,
                StartAddress = 0,
                Count = 10,
                PollingInterval = TimeSpan.FromSeconds(1)
            };
            
            Console.WriteLine($"✓ Valid range created: {validRange}");
            
            // Test range splitting
            var largeRange = new RegisterRange
            {
                RegisterType = ModbusRegisterType.HoldingRegister,
                UnitId = 1,
                StartAddress = 0,
                Count = 200, // Larger than max
                PollingInterval = TimeSpan.FromSeconds(1)
            };
            
            var splitRanges = largeRange.SplitToOptimalRanges().ToArray();
            Console.WriteLine($"✓ Large range split into {splitRanges.Length} smaller ranges");
            
            foreach (var range in splitRanges)
            {
                Console.WriteLine($"  Range: Start={range.StartAddress}, Count={range.Count}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Register range validation failed: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Test Modbus request creation
    /// </summary>
    public static void TestModbusRequestCreation()
    {
        Console.WriteLine("\nTesting Modbus request creation...");
        
        try
        {
            // Test read request
            var readRequest = new ModbusRequest
            {
                TransactionId = 1,
                UnitId = 1,
                FunctionCode = ModbusFunctionCode.ReadHoldingRegisters,
                StartingAddress = 0,
                Quantity = 10,
                Timeout = TimeSpan.FromSeconds(5)
            };
            
            var requestBytes = readRequest.ToByteArray();
            Console.WriteLine($"✓ Read request created: {requestBytes.Length} bytes");
            Console.WriteLine($"  Transaction ID: {readRequest.TransactionId}");
            Console.WriteLine($"  Function Code: {readRequest.FunctionCode}");
            Console.WriteLine($"  Start Address: {readRequest.StartingAddress}");
            Console.WriteLine($"  Quantity: {readRequest.Quantity}");
            
            // Test write request
            var writeRequest = new ModbusRequest
            {
                TransactionId = 2,
                UnitId = 1,
                FunctionCode = ModbusFunctionCode.WriteSingleRegister,
                StartingAddress = 0,
                Data = new byte[] { 0x12, 0x34 },
                Timeout = TimeSpan.FromSeconds(5)
            };
            
            var writeBytes = writeRequest.ToByteArray();
            Console.WriteLine($"✓ Write request created: {writeBytes.Length} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Modbus request creation failed: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Test Modbus response parsing
    /// </summary>
    public static void TestModbusResponseParsing()
    {
        Console.WriteLine("\nTesting Modbus response parsing...");
        
        try
        {
            // Test normal response (read holding registers)
            var normalResponseBytes = new byte[]
            {
                0x00, 0x01, // Transaction ID
                0x00, 0x00, // Protocol ID
                0x00, 0x07, // Length
                0x01,       // Unit ID
                0x03,       // Function Code (Read Holding Registers)
                0x04,       // Byte count
                0x12, 0x34, // Register 1
                0x56, 0x78  // Register 2
            };
            
            var normalResponse = ModbusResponse.Parse(normalResponseBytes);
            Console.WriteLine($"✓ Normal response parsed successfully");
            Console.WriteLine($"  Transaction ID: {normalResponse.TransactionId}");
            Console.WriteLine($"  Function Code: {normalResponse.FunctionCode}");
            Console.WriteLine($"  Is Exception: {normalResponse.IsException}");
            
            var registerValues = normalResponse.GetRegisterValues();
            Console.WriteLine($"  Register Values: [{string.Join(", ", registerValues)}]");
            
            // Test exception response
            var exceptionResponseBytes = new byte[]
            {
                0x00, 0x02, // Transaction ID
                0x00, 0x00, // Protocol ID
                0x00, 0x03, // Length
                0x01,       // Unit ID
                0x83,       // Function Code (Read Holding Registers + 0x80)
                0x02        // Exception Code (Illegal Data Address)
            };
            
            var exceptionResponse = ModbusResponse.Parse(exceptionResponseBytes);
            Console.WriteLine($"✓ Exception response parsed successfully");
            Console.WriteLine($"  Is Exception: {exceptionResponse.IsException}");
            Console.WriteLine($"  Exception Code: {exceptionResponse.ExceptionCode}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Modbus response parsing failed: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Test connection manager functionality
    /// </summary>
    public static async Task TestConnectionManager()
    {
        Console.WriteLine("\nTesting connection manager...");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithConnectionSettings(
                    connectionTimeout: TimeSpan.FromSeconds(2),
                    maxReconnectAttempts: 1)
                .Build();
            
            await modbusMaster.StartAsync();
            
            // Test connection info
            var connections = modbusMaster.ConnectionManager.GetConnectionInfo();
            Console.WriteLine($"✓ Initial connections: {connections.Count()}");
            
            // Test connection status for non-existent device
            var status = modbusMaster.ConnectionManager.GetConnectionStatus("192.168.1.999", 502);
            Console.WriteLine($"✓ Non-existent device status: {status}");
            
            await modbusMaster.StopAsync();
            Console.WriteLine("✓ Connection manager test completed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Connection manager test failed: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Test polling engine functionality
    /// </summary>
    public static async Task TestPollingEngine()
    {
        Console.WriteLine("\nTesting polling engine...");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithPollingSettings(
                    pollingCheckInterval: TimeSpan.FromMilliseconds(100),
                    enableChangeDetection: true)
                .Build();
            
            // Add a polling range
            var range = new RegisterRange
            {
                RegisterType = ModbusRegisterType.HoldingRegister,
                UnitId = 1,
                StartAddress = 0,
                Count = 5,
                PollingInterval = TimeSpan.FromSeconds(1)
            };
            
            modbusMaster.AddPollingRange("*************", 502, range);
            Console.WriteLine("✓ Polling range added");
            
            // Get polling ranges
            var ranges = modbusMaster.PollingEngine.GetPollingRanges("*************", 502);
            Console.WriteLine($"✓ Retrieved {ranges.Count()} polling ranges");
            
            // Remove polling range
            modbusMaster.RemovePollingRange("*************", 502, range);
            Console.WriteLine("✓ Polling range removed");
            
            ranges = modbusMaster.PollingEngine.GetPollingRanges("*************", 502);
            Console.WriteLine($"✓ After removal: {ranges.Count()} polling ranges");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Polling engine test failed: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Run all tests
    /// </summary>
    public static async Task RunAllTests()
    {
        Console.WriteLine("=== Ngp.Communication.ModbusTcpMaster Tests ===\n");
        
        TestModbusMasterCreation();
        TestRegisterRangeValidation();
        TestModbusRequestCreation();
        TestModbusResponseParsing();
        await TestConnectionManager();
        await TestPollingEngine();
        
        Console.WriteLine("\n=== Tests Completed ===");
    }
}
