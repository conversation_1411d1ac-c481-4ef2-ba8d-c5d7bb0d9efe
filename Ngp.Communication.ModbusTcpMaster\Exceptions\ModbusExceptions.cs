namespace Ngp.Communication.ModbusTcpMaster.Exceptions;

using Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Base exception for all Modbus-related errors
/// </summary>
public class ModbusException : Exception
{
    public ModbusException() { }
    public ModbusException(string message) : base(message) { }
    public ModbusException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when a Modbus slave returns an exception response
/// </summary>
public class ModbusSlaveException : ModbusException
{
    /// <summary>
    /// Function code that caused the exception
    /// </summary>
    public ModbusFunctionCode FunctionCode { get; }
    
    /// <summary>
    /// Modbus exception code returned by the slave
    /// </summary>
    public ModbusExceptionCode ExceptionCode { get; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; }
    
    public ModbusSlaveException(ModbusFunctionCode functionCode, ModbusExceptionCode exceptionCode, byte unitId)
        : base($"Modbus slave exception: Function {functionCode:X2}, Exception {exceptionCode:X2}, Unit {unitId}")
    {
        FunctionCode = functionCode;
        ExceptionCode = exceptionCode;
        UnitId = unitId;
    }
    
    public ModbusSlaveException(ModbusFunctionCode functionCode, ModbusExceptionCode exceptionCode, byte unitId, string message)
        : base(message)
    {
        FunctionCode = functionCode;
        ExceptionCode = exceptionCode;
        UnitId = unitId;
    }
}

/// <summary>
/// Exception thrown when a communication timeout occurs
/// </summary>
public class ModbusTimeoutException : ModbusException
{
    /// <summary>
    /// Timeout duration
    /// </summary>
    public TimeSpan Timeout { get; }
    
    /// <summary>
    /// Transaction ID that timed out
    /// </summary>
    public ushort TransactionId { get; }
    
    public ModbusTimeoutException(TimeSpan timeout, ushort transactionId)
        : base($"Modbus request timed out after {timeout.TotalMilliseconds}ms (Transaction ID: {transactionId})")
    {
        Timeout = timeout;
        TransactionId = transactionId;
    }
    
    public ModbusTimeoutException(TimeSpan timeout, ushort transactionId, string message)
        : base(message)
    {
        Timeout = timeout;
        TransactionId = transactionId;
    }
}

/// <summary>
/// Exception thrown when a connection error occurs
/// </summary>
public class ModbusConnectionException : ModbusException
{
    /// <summary>
    /// IP address of the device
    /// </summary>
    public string IpAddress { get; }
    
    /// <summary>
    /// Port number
    /// </summary>
    public int Port { get; }
    
    public ModbusConnectionException(string ipAddress, int port)
        : base($"Failed to connect to Modbus device at {ipAddress}:{port}")
    {
        IpAddress = ipAddress;
        Port = port;
    }
    
    public ModbusConnectionException(string ipAddress, int port, string message)
        : base(message)
    {
        IpAddress = ipAddress;
        Port = port;
    }
    
    public ModbusConnectionException(string ipAddress, int port, string message, Exception innerException)
        : base(message, innerException)
    {
        IpAddress = ipAddress;
        Port = port;
    }
}

/// <summary>
/// Exception thrown when an invalid response is received
/// </summary>
public class ModbusInvalidResponseException : ModbusException
{
    /// <summary>
    /// Raw response data
    /// </summary>
    public byte[] ResponseData { get; }
    
    /// <summary>
    /// Expected transaction ID
    /// </summary>
    public ushort ExpectedTransactionId { get; }
    
    /// <summary>
    /// Actual transaction ID received
    /// </summary>
    public ushort ActualTransactionId { get; }
    
    public ModbusInvalidResponseException(byte[] responseData, ushort expectedTransactionId, ushort actualTransactionId)
        : base($"Invalid Modbus response: Expected transaction ID {expectedTransactionId}, got {actualTransactionId}")
    {
        ResponseData = responseData;
        ExpectedTransactionId = expectedTransactionId;
        ActualTransactionId = actualTransactionId;
    }
    
    public ModbusInvalidResponseException(string message, byte[] responseData)
        : base(message)
    {
        ResponseData = responseData;
    }
}

/// <summary>
/// Exception thrown when a CRC error occurs (for RTU over TCP)
/// </summary>
public class ModbusCrcException : ModbusException
{
    /// <summary>
    /// Expected CRC value
    /// </summary>
    public ushort ExpectedCrc { get; }
    
    /// <summary>
    /// Actual CRC value
    /// </summary>
    public ushort ActualCrc { get; }
    
    /// <summary>
    /// Raw data that failed CRC check
    /// </summary>
    public byte[] RawData { get; }
    
    public ModbusCrcException(ushort expectedCrc, ushort actualCrc, byte[] rawData)
        : base($"CRC error: Expected {expectedCrc:X4}, got {actualCrc:X4}")
    {
        ExpectedCrc = expectedCrc;
        ActualCrc = actualCrc;
        RawData = rawData;
    }
}

/// <summary>
/// Exception thrown when an invalid register range is specified
/// </summary>
public class ModbusInvalidRangeException : ModbusException
{
    /// <summary>
    /// Starting address
    /// </summary>
    public ushort StartAddress { get; }
    
    /// <summary>
    /// Quantity of registers/coils
    /// </summary>
    public ushort Quantity { get; }
    
    /// <summary>
    /// Register type
    /// </summary>
    public ModbusRegisterType RegisterType { get; }
    
    public ModbusInvalidRangeException(ushort startAddress, ushort quantity, ModbusRegisterType registerType)
        : base($"Invalid register range: {registerType} starting at {startAddress} with quantity {quantity}")
    {
        StartAddress = startAddress;
        Quantity = quantity;
        RegisterType = registerType;
    }
    
    public ModbusInvalidRangeException(ushort startAddress, ushort quantity, ModbusRegisterType registerType, string message)
        : base(message)
    {
        StartAddress = startAddress;
        Quantity = quantity;
        RegisterType = registerType;
    }
}
